from binance.error import ClientError
from binance.um_futures import UMFutures

from config import settings
from util import Telegram


bot_token = settings.telegram.binance_bot


class Binance_log:

    def __init__(self, logger=None) -> None:
        self.um_futures_client = UMFutures(
            key=settings.binance.zach_account.api_key,
            secret=settings.binance.zach_account.api_secret,
        )

        self.logger = logger
        self.update_accountInfo()
        self.telegram = Telegram(bot_token, logger)
        self.telegram.send_message("Binance Log Telegram inited.")

    def update_accountInfo(self):
        try:
            response = self.um_futures_client.account(recvWindow=6000)
            if "assets" in response:
                for asset in response["assets"]:
                    if asset["asset"] == "USDT":
                        self.asset = float(asset["walletBalance"]) + float(
                            asset["crossUnPnl"]
                        )
                self.logger.info(f"ZACH asset: {self.asset}")
            if "positions" in response:
                positions = {}
                for position in response["positions"]:
                    if float(position["positionAmt"]) != 0:
                        if position["symbol"] not in positions:
                            positions[position["symbol"]] = {}
                        positions[position["symbol"]][position["positionSide"]] = abs(
                            float(position["positionAmt"])
                        )
                self.positions = positions
                self.logger.info(f"ZACH positions: {self.positions}")
        except ClientError as error:
            self.logger.error(
                f"Found error. status: {error.status_code}, error code: {error.error_code}, error message: {error.error_message}"
            )
        except Exception as e:
            self.logger.error(e, exc_info=True)

    def close_remain_position(self, symbol, side, positionSide):
        self.update_accountInfo()

        if symbol in self.positions and positionSide in self.positions[symbol]:
            quantity = self.positions[symbol][positionSide]
            try:
                response = self.um_futures_client.new_order(
                    symbol=symbol,
                    side=side,
                    type="MARKET",
                    quantity=quantity,
                    positionSide=positionSide,
                )
                if "executedQty" in response:
                    msg = f"[清仓]【{side}】 {symbol}，平仓【{quantity}】个，成交【{response['executedQty']}个】"
                    self.telegram.send_message(msg)
                    self.logger.info(msg)
                else:
                    self.telegram.send_message(response)
                    self.logger.error(response)
            except ClientError as error:
                self.logger.error(
                    f"Found error. status: {error.status_code}, error code: {error.error_code}, error message: {error.error_message}"
                )
                self.telegram.send_message(error.error_message)
            except Exception as e:
                self.telegram.send_message(
                    f"平仓失败请检查原因：【{side}】 {symbol}，平仓【{quantity}】个"
                )
                self.telegram.send_message(e)
                self.logger.error(e, exc_info=True)
        self.cancel_orders(symbol, positionSide)

    def cancel_orders(self, symbol, positionSide=None):
        if not positionSide:
            try:
                response = self.um_futures_client.cancel_open_orders(symbol)
                self.logger.info(f"cancel all {symbol} orders")
                self.logger.info(response)
            except ClientError as error:
                self.logger.error(
                    f"Found error. status: {error.status_code}, error code: {error.error_code}, error message: {error.error_message}"
                )
                self.telegram.send_message(error.error_message)
            except Exception as e:
                self.telegram.send_message("订单取消失败")
                self.telegram.send_message(e)
                self.logger.error(e, exc_info=True)
        else:
            try:
                cancelOrders = []
                openOrders = self.um_futures_client.get_orders(symbol=symbol)
                for order in openOrders:
                    if order["positionSide"] == positionSide:
                        cancelOrders.append(order["orderId"])
                for i in range(0, len(cancelOrders), 10):
                    response = self.um_futures_client.cancel_batch_order(
                        symbol=symbol,
                        orderIdList=cancelOrders[i : i + 10],
                        origClientOrderIdList=None,
                    )
                    self.logger.info(f"cancel all {symbol} {positionSide} orders")
                    self.logger.info(response)
            except Exception as e:
                self.logger.error(e, exc_info=True)
                self.telegram.send_message(e)
