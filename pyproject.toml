###############################################
# Bicoin Follow Trade: tooling configuration  #
# 仅保留本项目需要的 Ruff / MyPy 配置              #
###############################################

# 代码格式化
[tool.black]
line-length = 100
target-version = ["py312"]
extend-exclude = '(backup|log|__pycache__|\.mypy_cache|\.ruff_cache|\.git)'

# 代码检查与格式
[tool.ruff]
line-length = 100
target-version = "py312"
extend-exclude = [
  "backup",
  "log",
  "__pycache__",
  ".mypy_cache",
  ".ruff_cache",
  ".git",
]

[tool.ruff.lint]
extend-select = [
  "C90",    # mccabe
  "B",      # bugbear
  # "N",    # pep8-naming
  "F",      # pyflakes
  "E",      # pycodestyle
  "W",      # pycodestyle
  "UP",     # pyupgrade
  "I",      # isort
  "A",      # flake8-builtins
  "TID",    # flake8-tidy-imports
  # "EXE",  # flake8-executable
  # "C4",     # flake8-comprehensions
  "YTT",    # flake8-2020
  "S",      # flake8-bandit
  # "DTZ",  # flake8-datetimez
  # "RSE",  # flake8-raise
  # "TCH",  # flake8-type-checking
  "PTH",    # flake8-use-pathlib
  "RUF",    # ruff
  "ASYNC",  # flake8-async
  "NPY",    # numpy
]

extend-ignore = [
  "E241",  # Multiple spaces after comma
  "E272",  # Multiple spaces before keyword
  "E221",  # Multiple spaces before operator
  "B007",  # Loop control variable not used
  "B904",  # BugBear - except raise from
  "S603",  # `subprocess` call: check for execution of untrusted input
  "S607",  # Starting a process with a partial executable path
  "S608",  # Possible SQL injection vector through string-based query construction
  "NPY002",  # Numpy legacy random generator
  "RUF010",  # Use explicit conversion flag
  "RUF012",  # mutable-class-default
  "RUF022",  # unsorted-dunder-all
  "RUF005",  # list concatenation
]

[tool.ruff.lint.mccabe]
max-complexity = 12

[tool.ruff.lint.isort]
lines-after-imports = 2


# 类型检查
[tool.mypy]
python_version = "3.12"
ignore_missing_imports = true
namespace_packages = false
warn_unused_ignores = true
exclude = [
  "backup",
  "log",
  "__pycache__",
  ".mypy_cache",
  ".ruff_cache",
  ".git",
]
