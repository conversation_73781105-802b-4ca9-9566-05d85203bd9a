"""
Configuration management
- .secrets.yaml: Static sensitive credentials (managed by Dynaconf)
- config.yaml: Runtime configuration (direct yaml operations for read/write)
"""

import os

import yaml
from dynaconf import Dynaconf


# Only manage .secrets.yaml with Dynaconf (read-only sensitive config)
settings = Dynaconf(
    settings_files=[".secrets.yaml"],
    environments=False,
    load_dotenv=False,
    envvar_prefix=None,
)


# Helper functions for .secrets.yaml (sensitive config)
def get_telegram_token(bot_name):
    """Get telegram bot token by name"""
    return settings.telegram[bot_name]


def get_binance_credentials(account_name):
    """Get Binance API credentials by account name"""
    account = settings.binance[account_name]
    return account["api_key"], account["api_secret"]


# Helper functions for config.yaml (runtime config - read/write)
CONFIG_FILE = "config.yaml"


def load_runtime_config():
    """Load runtime configuration from config.yaml"""
    if os.path.exists(CONFIG_FILE):
        with open(CONFIG_FILE, encoding="utf-8") as f:
            return yaml.safe_load(f) or {}
    return {}


def save_runtime_config(config_data):
    """Save runtime configuration to config.yaml"""
    with open(CONFIG_FILE, "w", encoding="utf-8") as f:
        yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)


def get_account_asset(account_name):
    """Get account asset amount from config.yaml"""
    config = load_runtime_config()
    key = f"{account_name}_asset"
    return config.get(key, 0)


def get_account_margin(account_name):
    """Get account margin multiplier from config.yaml"""
    config = load_runtime_config()
    key = f"{account_name}_margin"
    return config.get(key, 1)


def update_account_asset(account_name, asset_value):
    """Update account asset in config.yaml"""
    config = load_runtime_config()
    key = f"{account_name}_asset"
    config[key] = asset_value
    save_runtime_config(config)


def update_account_margin(account_name, margin_value):
    """Update account margin in config.yaml"""
    config = load_runtime_config()
    key = f"{account_name}_margin"
    config[key] = margin_value
    save_runtime_config(config)
